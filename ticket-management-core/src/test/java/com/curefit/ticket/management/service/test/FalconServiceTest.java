package com.curefit.ticket.management.service.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static com.curefit.odin.commons.Constants.DISABLED_USER;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.falcon.*;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.utils.service.FalconService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import com.curefit.common.data.exception.BaseException;
import com.curefit.falcon.client.SearchClient;
import com.curefit.falcon.request.SearchRequest;
import com.curefit.falcon.response.SearchResponse;

@RunWith(MockitoJUnitRunner.class)
public class FalconServiceTest {

    private static final String TEST_EMAIL = "<EMAIL>";
    private static final String TEST_QUERY = "test query";
    private static final String FALCON_API_KEY = "test-api-key";
    private static final String FALCON_ETL_URL = "http://test-falcon-etl.com/refresh";

    @Mock
    private SearchClient searchClient;

    @Mock
    private CommonHttpHelper commonHttpHelper;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private FalconService falconService;

    @Before
    public void initMocks() {
        MockitoAnnotations.initMocks(this);
        // Set up configuration values using ReflectionTestUtils
        ReflectionTestUtils.setField(falconService, "falconApiKey", FALCON_API_KEY);
        ReflectionTestUtils.setField(falconService, "falconEtlUrl", FALCON_ETL_URL);
    }

    @Test
    public void testFindUserByEmailId_Success() throws BaseException {
        // Arrange
        SearchResponse searchResponse = new SearchResponse();
        BaseIndexEntry baseIndexEntry = new BaseIndexEntry();
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("name", "Test User");
        attributes.put("emailId", TEST_EMAIL);
        attributes.put("isDl", false);
        attributes.put("memberCount", 1);
        baseIndexEntry.setAttributes((ObjectNode) attributes);
        searchResponse.setResults(Collections.singletonList(baseIndexEntry));

        UserEntry expectedUserEntry = new UserEntry("Test User", TEST_EMAIL, false, 1);

        when(searchClient.search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false)))
                .thenReturn(searchResponse);
        when(objectMapper.convertValue(any(Map.class), eq(UserEntry.class)))
                .thenReturn(expectedUserEntry);

        // Act
        UserEntry actualUserEntry = falconService.findUserByEmailId(TEST_EMAIL);

        // Assert
        assertNotNull(actualUserEntry);
        assertEquals(expectedUserEntry.getName(), actualUserEntry.getName());
        assertEquals(expectedUserEntry.getEmailId(), actualUserEntry.getEmailId());
        verify(searchClient, times(1)).search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false));
        verify(objectMapper, times(1)).convertValue(any(Map.class), eq(UserEntry.class));
    }

    @Test
    public void testFindUserByEmailId_NoResults() throws BaseException {
        // Arrange
        SearchResponse searchResponse = new SearchResponse();
        searchResponse.setResults(new ArrayList<>());

        when(searchClient.search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false)))
                .thenReturn(searchResponse);

        // Act
        UserEntry actualUserEntry = falconService.findUserByEmailId(TEST_EMAIL);

        // Assert
        assertNotNull(actualUserEntry);
        assertEquals(DISABLED_USER, actualUserEntry.getName());
        assertEquals(TEST_EMAIL, actualUserEntry.getEmailId());
        assertEquals(false, actualUserEntry.getIsDl());
        assertEquals(Integer.valueOf(1), actualUserEntry.getMemberCount());
        verify(searchClient, times(1)).search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false));
    }

    @Test(expected = RuntimeException.class)
    public void testFindUserByEmailId_SearchClientThrowsException() throws BaseException {
        // Arrange
        when(searchClient.search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false)))
                .thenThrow(new RuntimeException("Search client error"));

        // Act
        falconService.findUserByEmailId(TEST_EMAIL);

        // Assert - Exception should be thrown
    }

    @Test
    public void testGetSearchRequest_ValidQuery() {
        SearchRequest searchRequest = falconService.getSearchRequest(TEST_QUERY);
        System.out.println("SearchRequest applied filters: "+searchRequest.getAppliedFilters());
        assertNotNull(searchRequest);
        assertEquals(TEST_QUERY, searchRequest.getQuery());

        assertNotNull(searchRequest.getLocations());
        assertEquals(1, searchRequest.getLocations().size());
        assertEquals("IN", searchRequest.getLocations().getFirst().getCountryId());
        assertEquals("Bangalore", searchRequest.getLocations().getFirst().getCityId());
        assertNotNull(searchRequest.getAppliedFilters());
        assertFalse(searchRequest.getAppliedFilters().isEmpty());
    }

    @Test
    public void testGetSearchRequest_NullQuery() {
        SearchRequest searchRequest = falconService.getSearchRequest(null);
        assertNotNull(searchRequest);
        assertNull(searchRequest.getQuery());
        assertEquals(1,searchRequest.getPageNumber().intValue());
        assertEquals(20, searchRequest.getCount().intValue());
    }

    @Test
    public void testGetSearchRequest_EmptyQuery() {
        // Act
        SearchRequest searchRequest = falconService.getSearchRequest("");

        // Assert
        assertNotNull(searchRequest);
        assertEquals("", searchRequest.getQuery());
        assertEquals(1,searchRequest.getPageNumber().intValue());
        assertEquals(20, searchRequest.getCount().intValue());
    }

    @Test
    public void testGetAppliedFilters_Structure() {
        // Act
        List<Filter> appliedFilters = falconService.getAppliedFilters();

        // Assert
        assertNotNull(appliedFilters);
        assertEquals(1, appliedFilters.size());

        // Verify top-level vertical filter
        Filter verticalFilter = appliedFilters.getFirst();
        assertNotNull(verticalFilter);
        assertEquals("vertical", verticalFilter.getKey());
        assertEquals("Vertical", verticalFilter.getTitle());
        assertEquals(FilterType.SELECT, verticalFilter.getType());
        assertNotNull(verticalFilter.getValues());
        assertEquals(1, verticalFilter.getValues().size());

        // Verify ODIN value
        SelectFilterValue odinValue = (SelectFilterValue) verticalFilter.getValues().getFirst();
        assertEquals("ODIN", odinValue.getValue());
        assertEquals("ODIN", odinValue.getTitle());
        assertNotNull(odinValue.getNestedFilters());
    }

    @Test
    public void testGetAppliedFilters_NestedStructure() {
        // Act
        List<Filter> appliedFilters = falconService.getAppliedFilters();

        // Assert - Verify nested structure
        Filter verticalFilter = appliedFilters.getFirst();
        SelectFilterValue odinValue = (SelectFilterValue) verticalFilter.getValues().getFirst();

        // Verify SubVertical filter
        Filter subVerticalFilter = odinValue.getNestedFilters().getFirst();
        assertEquals("subVertical", subVerticalFilter.getKey());
        assertEquals("SubVertical", subVerticalFilter.getTitle());
        assertEquals(FilterType.SELECT, subVerticalFilter.getType());
        assertNotNull(subVerticalFilter.getValues());
        assertEquals(1, subVerticalFilter.getValues().size());

        // Verify TICKETING_SYSTEM value
        SelectFilterValue ticketingSystemValue = (SelectFilterValue) subVerticalFilter.getValues().getFirst();
        assertEquals("TICKETING_SYSTEM", ticketingSystemValue.getValue());
        assertEquals("TICKETING_SYSTEM", ticketingSystemValue.getTitle());
        assertNotNull(ticketingSystemValue.getNestedFilters());
        assertEquals(1, ticketingSystemValue.getNestedFilters().size());

        // Verify Entity filter
        Filter entityFilter = ticketingSystemValue.getNestedFilters().getFirst();
        assertEquals("attributes.entityDisplayName", entityFilter.getKey());
        assertEquals("Entity", entityFilter.getTitle());
        assertEquals(FilterType.SELECT, entityFilter.getType());
        assertNotNull(entityFilter.getValues());
        assertEquals(1, entityFilter.getValues().size());

        // Verify User value
        SelectFilterValue userValue = (SelectFilterValue) entityFilter.getValues().getFirst();
        assertEquals("User", userValue.getValue());
        assertEquals("User", userValue.getTitle());
    }

    @Test
    public void testGetAppliedFilters_Consistency() {
        // Act - Call multiple times
        List<Filter> filters1 = falconService.getAppliedFilters();
        List<Filter> filters2 = falconService.getAppliedFilters();

        // Assert - Should return consistent structure
        assertNotNull(filters1);
        assertNotNull(filters2);
        assertEquals(filters1.size(), filters2.size());

        // Verify structure is the same
        assertEquals(filters1.getFirst().getKey(), filters2.getFirst().getKey());
        assertEquals(filters1.getFirst().getTitle(), filters2.getFirst().getTitle());
        assertEquals(filters1.getFirst().getType(), filters2.getFirst().getType());
    }

    @Test
    public void testFindUserByEmailId_WithNullEmail() throws BaseException {
        // Arrange
        SearchResponse searchResponse = new SearchResponse();
        searchResponse.setResults(new ArrayList<>());
        when(searchClient.search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false)))
                .thenReturn(searchResponse);

        // Act
        UserEntry actualUserEntry = falconService.findUserByEmailId(null);

        // Assert
        assertNotNull(actualUserEntry);
        assertEquals(DISABLED_USER, actualUserEntry.getName());
        assertNull(actualUserEntry.getEmailId());
        verify(searchClient, times(1)).search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false));
    }

    @Test
    public void testFindUserByEmailId_WithEmptyEmail() throws BaseException {
        // Arrange
        SearchResponse searchResponse = new SearchResponse();
        searchResponse.setResults(new ArrayList<>());
        when(searchClient.search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false)))
                .thenReturn(searchResponse);

        // Act
        UserEntry actualUserEntry = falconService.findUserByEmailId("");

        // Assert
        assertNotNull(actualUserEntry);
        assertEquals(DISABLED_USER, actualUserEntry.getName());
        assertEquals("", actualUserEntry.getEmailId());
        verify(searchClient, times(1)).search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false));
    }

    @Test
    public void testFindUsersByQuery_Success() throws BaseException {
        // Arrange
        SearchResponse searchResponse = new SearchResponse();
        BaseIndexEntry baseIndexEntry1 = new BaseIndexEntry();
        BaseIndexEntry baseIndexEntry2 = new BaseIndexEntry();

        Map<String, Object> attributes1 = new HashMap<>();
        attributes1.put("name", "User One");
        attributes1.put("emailId", "use <EMAIL>");
        baseIndexEntry1.setAttributes(objectMapper.convertValue(attributes1, ObjectNode.class));

        Map<String, Object> attributes2 = new HashMap<>();
        attributes2.put("name", "User Two");
        attributes2.put("emailId", "<EMAIL>");
        baseIndexEntry2.setAttributes(objectMapper.convertValue(attributes2, ObjectNode.class));

        List<BaseIndexEntry> results = new ArrayList<>();
        results.add(baseIndexEntry1);
        results.add(baseIndexEntry2);
        searchResponse.setResults(results);

        UserEntry userEntry1 = new UserEntry("User One", "<EMAIL>");
        UserEntry userEntry2 = new UserEntry("User Two", "<EMAIL>");

        when(searchClient.search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false)))
                .thenReturn(searchResponse);
        when(objectMapper.convertValue(attributes1, UserEntry.class)).thenReturn(userEntry1);
        when(objectMapper.convertValue(attributes2, UserEntry.class)).thenReturn(userEntry2);

        // Act
        List<UserEntry> actualUsers = falconService.findUsersByQuery(TEST_QUERY);

        // Assert
        assertNotNull(actualUsers);
        assertEquals(2, actualUsers.size());
        assertEquals("User One", actualUsers.get(0).getName());
        assertEquals("User Two", actualUsers.get(1).getName());
        verify(searchClient, times(1)).search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false));
        verify(objectMapper, times(2)).convertValue(any(Map.class), eq(UserEntry.class));
    }

    @Test
    public void testFindUsersByQuery_NoResults() throws BaseException {
        // Arrange
        SearchResponse searchResponse = new SearchResponse();
        searchResponse.setResults(new ArrayList<>());

        when(searchClient.search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false)))
                .thenReturn(searchResponse);

        // Act
        List<UserEntry> actualUsers = falconService.findUsersByQuery(TEST_QUERY);

        // Assert
        assertNotNull(actualUsers);
        assertTrue(actualUsers.isEmpty());
        verify(searchClient, times(1)).search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false));
    }

    @Test(expected = BaseException.class)
    public void testFindUsersByQuery_BaseExceptionThrown() throws BaseException {
        // Arrange
        when(searchClient.search(any(SearchRequest.class), eq("odin"), anyString(), isNull(), isNull(), eq(false)))
                .thenThrow(new BaseException("Search error"));

        // Act
        falconService.findUsersByQuery(TEST_QUERY);

        // Assert - Exception should be thrown
    }

    @Test
    public void testRefreshUsers_Success() {
        // Arrange
        ResponseEntity<Object> successResponse = new ResponseEntity<>("Success", HttpStatus.OK);
        when(commonHttpHelper.request(anyString(), eq(HttpMethod.POST), isNull(), any(Map.class), eq(Object.class)))
                .thenReturn(successResponse);

        // Act
        falconService.refreshUsers();

        // Assert
        String expectedUrl = FALCON_ETL_URL + "?vertical=ODIN&subVertical=TICKETING_SYSTEM&entityType=USER";
        verify(commonHttpHelper, times(1)).request(eq(expectedUrl), eq(HttpMethod.POST), isNull(), any(Map.class), eq(Object.class));
    }

    @Test
    public void testRefreshUsers_NonSuccessResponse() {
        // Arrange
        ResponseEntity<Object> errorResponse = new ResponseEntity<>("Error", HttpStatus.INTERNAL_SERVER_ERROR);
        when(commonHttpHelper.request(anyString(), eq(HttpMethod.POST), isNull(), any(Map.class), eq(Object.class)))
                .thenReturn(errorResponse);

        // Act
        falconService.refreshUsers();

        // Assert
        String expectedUrl = FALCON_ETL_URL + "?vertical=ODIN&subVertical=TICKETING_SYSTEM&entityType=USER";
        verify(commonHttpHelper, times(1)).request(eq(expectedUrl), eq(HttpMethod.POST), isNull(), any(Map.class), eq(Object.class));
    }

    @Test
    public void testRefreshUsers_ExceptionHandled() {
        // Arrange
        when(commonHttpHelper.request(anyString(), eq(HttpMethod.POST), isNull(), any(Map.class), eq(Object.class)))
                .thenThrow(new RuntimeException("HTTP error"));

        // Act - Should not throw exception, should handle it gracefully
        falconService.refreshUsers();

        // Assert
        String expectedUrl = FALCON_ETL_URL + "?vertical=ODIN&subVertical=TICKETING_SYSTEM&entityType=USER";
        verify(commonHttpHelper, times(1)).request(eq(expectedUrl), eq(HttpMethod.POST), isNull(), any(Map.class), eq(Object.class));
    }
}
